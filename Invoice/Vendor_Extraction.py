import sys
import re
import json
import time
from scipy.spatial import distance as sp_distance
from fuzzywuzzy import fuzz
from spacy.matcher import Matcher
import pandas as pd
from fuzzywuzzy import fuzz
from Invoice.ML_Vendor_Extraction import *
import json
import pandas as pd
#import lexnlp.extract.en.entities.nltk_re
from PIL import Image
#import Image
#import lexnlp.extract.en.urls
from urllib.parse import urlparse
import traceback
import tldextract
from Invoice.DbConfig import *
from spacy.matcher import PhraseMatcher,Matcher

#extract object initiated 
#extract = tldextract.TLDExtratct()

def getTextFromBox(data,box,im_width,im_height):

    # f = open(json_path,) 
    # data = json.load(f)
    
    x1= box[0] -50
    x2= box[1] + 100
    y1= box[2] 
    y2= box[3]
    print("get text from box ",y1,x1,y2,x2)
    text=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":

            BoundingBox=d["Geometry"]["BoundingBox"]
            add=d["Geometry"]["Polygon"]

             
            y1_= (add[0]["Y"]*im_height)
            y2_= (add[3]["Y"]*im_height)
            x1_= (add[0]["X"]*im_width)
            x2_= (add[2]["X"]*im_width)
            
            #print(d["Text"],"--",y1_,x1_,y2_,x2_)
            
            if y1_>y1 and y2_<y2 and x1_>x1 and x2_<x2:
                # print("ex text ",d["Text"])
                if len(d["Text"])>=3:
                    text.append(d["Text"])
    print("text from box ",text)
    return text 

def getVendorByTop(data,im_height,vendorlist,config_name,general_entity_fields):

    # f = open(json_path) 
    # data = json.load(f)

    lines=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":
            top=d["Geometry"]["BoundingBox"]["Top"]
            lines.append( (d["Text"],top) )
            if top>(im_height*30):
                break
    
    f=[]
    for l in lines:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(l[0]))
        r=spacy_getorg(l[0],config_name,general_entity_fields)
        
        #if vendor list is from blocked content skip this vendor name
        if getfuzz_score_list(vendorlist,l[0]) is not None:
            continue
                        
        if len(r)>0:
            f.append(l)
            #print(l)
    print("all mathched results for vendors ",f)
    if len(f)>0:        
        f.sort(key = lambda x: x[1])
        return f[0]
    else:
        return None

def spacy_getorg(text, config_name, gen_fields):
    """Optimized organization extraction using cached phrase matcher."""
    if not text or not text.strip():
        return []

    res = []

    # Use cached phrase matcher
    pharase_matcher, entity = get_cached_phrase_matcher(config_name, gen_fields)

    # Use cached nlp document
    doc = get_cached_nlp_doc(text)

    # Get standard ORG entities
    for e in doc.ents:
        if e.label_ == "ORG":
            res.append(e.text)

    # Get custom pattern matches
    if entity:  # Only run if we have entities to match
        matches2 = pharase_matcher(doc)
        for _, start, end in matches2:
            span = doc[start:end]
            match_res = span.text
            if match_res not in res:  # Avoid duplicates
                res.append(match_res)

    return res

def spacy_geturl(text):
    urls=[]
    doc=nlp(text)
    for e in doc:
        if e.like_url:
            urls.append(e)
    
    return urls
 
def getVendorbyMatch(boxtext, company_list, config_name, general_entity_fields):
    """Optimized vendor matching with early termination."""
    if not boxtext:
        return None

    for text_line in boxtext:
        if not text_line or len(text_line) < 3:
            continue

        r = spacy_getorg(text_line, config_name, general_entity_fields)

        if len(r) > 0:
            # Exclusion rule for removing block lists from vendor names
            fuzz_result = getfuzz_score_list(company_list, r[0])
            if fuzz_result is None:  # Not in blocklist
                return r[0]  # Return first valid match for performance

    return None

def get_fuzz_score(str1, str2):
    partial_ratio = fuzz.partial_ratio(str1.lower(), str2.lower())
    return partial_ratio

def getfuzz_score_list(arr,str1):    
    final_match=None
    for v in arr :
        ratio = fuzz.ratio(v.lower(), str1.lower())
        if ratio>85:
            final_match=v    
    return final_match

def getVendorByMail(data,vendorlist):
    
    email_list=[]
    url_list=[]

    # with open(json_path) as json_file:
    #     data=json.load(json_file)
    
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":
            #print(d["Text"])
            text= d["Text"]
            emails = re.findall(r"[a-z0-9\.\-+_]+@[a-z0-9\.\-+_]+\.[a-z]+", text)
            #url=list(lexnlp.extract.en.urls.get_urls(text))
            url=spacy_geturl(text)
            #print(url )
            if len(url)>0:
                #parsed_uri = urlparse(str(url[0]))

                #if parsed_uri.netloc!="":
                #    domain=parsed_uri.netloc.split(".")[0]
                #else:
                #    domain=parsed_uri.path.split(".")[0]
                res=tldextract.extract(str(url[0]) )
                domain=str(res.domain)
                #domain=parsed_uri.netloc.split(".")[0]
                if domain is not None:
                    if getfuzz_score_list(vendorlist,domain) is None:
                        url_list.append(domain)
            if len(emails)>0:
                # print("emails found ",emails,getfuzz_score_list(vendorlist,emails[0]))
                if getfuzz_score_list(vendorlist,emails[0]) is None:
                    email_list.append(emails[0].split("@")[1].split(".")[0])
    
    # print("url list ",url_list)
    # print("email list ",email_list)
    url_list=[x for x in url_list if x]
    email_list=[x for x in email_list if x]

    if len(url_list)>0:
        return url_list[0]
    
    if len(email_list) >0:
        return email_list[0]

    return None

def findfullyqualifiedcompanyname(data, vendorname, vendorlist, general_entity_fields, config_name):
    """Optimized company name finder with early termination and caching."""
    if not vendorname:
        return vendorname

    # Extract all line text efficiently
    text_data = [
        block["Text"] for block in data.get('Blocks', [])
        if block.get("BlockType") == 'LINE' and block.get("Text")
    ]

    if not text_data:
        return vendorname

    # Extract company names with early termination
    company_list = []
    processed_texts = set()  # Avoid processing duplicate texts
    vendorname_lower = vendorname.lower()

    for text_line in text_data:
        if text_line in processed_texts:
            continue
        processed_texts.add(text_line)

        # Quick check if text might contain the vendor name
        if vendorname_lower in text_line.lower():
            r = spacy_getorg(text_line, config_name, general_entity_fields)

            if len(r) > 0:
                # Check if vendor is not in blocklist
                if getfuzz_score_list(vendorlist, r[0]) is None:
                    company_list.append(r[0])

                    # Early termination if we find a very close match
                    quick_score = get_fuzz_score(r[0], vendorname)
                    if quick_score >= 95:
                        return r[0]  # Return immediately for near-perfect matches

    if not company_list:
        return vendorname

    # Find best match with early termination
    best_match = None
    best_score = 90  # Minimum threshold

    for company in company_list:
        score = get_fuzz_score(company, vendorname)
        if score > best_score:
            best_score = score
            best_match = company

            # Early termination for perfect matches
            if score >= 98:
                break

    return best_match if best_match else vendorname

def getVendorByLogoMatch(data, boxtext, vendorlist, config_name, general_entity_fields):
    """Optimized vendor matching by logo with improved performance."""
    print("IN LOGO MATCH")

    if not boxtext:
        return []

    # Extract all line text efficiently
    text_data = [
        block["Text"] for block in data.get('Blocks', [])
        if block.get("BlockType") == 'LINE' and block.get("Text")
    ]

    if not text_data:
        return []

    print("length of text data", len(text_data))

    # Extract company names with early termination and batch processing
    company_list = []
    processed_texts = set()  # Avoid processing duplicate texts

    for text_line in text_data:
        if text_line in processed_texts:
            continue
        processed_texts.add(text_line)

        r = spacy_getorg(text_line, config_name, general_entity_fields)

        if len(r) > 0:
            # Check if vendor is not in blocklist
            if getfuzz_score_list(vendorlist, r[0]) is None:
                company_list.append(r[0])

                # Early termination if we have enough companies
                if len(company_list) >= 50:  # Limit processing for performance
                    break

    if not company_list:
        return []

    # Optimized matching with early termination
    result = []
    boxtext_lower = [j.lower() for j in boxtext if len(j) >= 3]  # Pre-process boxtext

    for company in company_list:
        company_lower = company.lower()
        best_score = 0

        for boxtext_item in boxtext_lower:
            score = fuzz.partial_ratio(company_lower, boxtext_item)

            if score >= 85 and score > best_score:
                best_score = score

                # Early termination for perfect matches
                if score >= 95:
                    break

        if best_score >= 85:
            result.append([company, best_score])

            # Early termination if we have high-confidence matches
            if best_score >= 95 and len(result) >= 3:
                break

    return result

# global_config=None

# Global cache for spacy processing to avoid repeated initialization
_phrase_matcher_cache = {}
_nlp_doc_cache = {}

def get_cached_phrase_matcher(config_name, gen_fields):
    """Get or create cached phrase matcher for given config and fields."""
    cache_key = f"{config_name}_{hash(str(gen_fields))}"

    if cache_key not in _phrase_matcher_cache:
        pharase_matcher = PhraseMatcher(nlp.vocab)
        entity = []

        for item in gen_fields:
            if item.get('fieldtype') == "ORG" and item.get('entity'):
                entity.append(item['entity'])

        if len(entity) > 0:
            patterns = [nlp(e) for e in entity]
            pharase_matcher.add("ENTITY_PATTERN", patterns)

        _phrase_matcher_cache[cache_key] = (pharase_matcher, entity)

    return _phrase_matcher_cache[cache_key]

def get_cached_nlp_doc(text):
    """Get or create cached nlp document for text."""
    if text not in _nlp_doc_cache:
        _nlp_doc_cache[text] = nlp(text)
        # Limit cache size to prevent memory issues
        if len(_nlp_doc_cache) > 1000:
            # Remove oldest entries
            keys_to_remove = list(_nlp_doc_cache.keys())[:100]
            for key in keys_to_remove:
                del _nlp_doc_cache[key]

    return _nlp_doc_cache[text]

def clear_spacy_cache():
    """Clear the spacy caches to free memory."""
    global _phrase_matcher_cache, _nlp_doc_cache
    _phrase_matcher_cache.clear()
    _nlp_doc_cache.clear()
    print("Spacy caches cleared")

def getVendors(model,image,data,companylist,config_name,response_layer,images_list):

    #fetching the general entities once from database
    start_time = time.time()
    print(f"Starting get_General_entity at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
    general_entity_fields=get_General_entity(config_name)
    end_time = time.time()
    print(f"Completed get_General_entity in {end_time - start_time:.4f} seconds")

    # global_config=config_name
    #vendor_list=["td williamson","welldyne"]
    # print("companylist ...... ",companylist)
    vendor_list=[]
    if companylist is not None:
        vendor_list=companylist
        #global_vendorlist=company_list

    # print("vendor list ... ",vendor_list)

    start_time = time.time()
    print(f"Starting Image.open at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
    im = Image.open(image)
    end_time = time.time()
    print(f"Completed Image.open in {end_time - start_time:.4f} seconds")
    im_width, im_height = im.size
    # print("image dim ",im_height,im_width)
    #call ML Model to get the Results
    p_res=None
    boxtext=[]
    try:
        start_time = time.time()
        print(f"Starting predict at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
        p_res=predict(model,image,im_width,im_height)
        end_time = time.time()
        print(f"Completed predict in {end_time - start_time:.4f} seconds")
        # print("box value extracted ",p_res)

    except Exception:

        print("Error While ML Vendor Extraction")
        print(traceback.format_exc())


    if p_res is None:
        if not response_layer.executed:
                start_time = time.time()
                print(f"Starting response_layer.execute at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
                response_layer.execute(images_list)
                end_time = time.time()
                print(f"Completed response_layer.execute in {end_time - start_time:.4f} seconds")
        if response_layer.vendor_name is not None and len(response_layer.vendor_name)>0:
            print("vendor name API 2 ", response_layer.vendor_name)
            #p_res= (response_layer.vendor_name,88)
            start_time = time.time()
            print(f"Starting getVendorbyMatch at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
            match_item=getVendorbyMatch([response_layer.vendor_name],companylist,config_name,general_entity_fields)
            end_time = time.time()
            print(f"Completed getVendorbyMatch in {end_time - start_time:.4f} seconds")
            print("get vendr by match API2 ",match_item)
            start_time = time.time()
            print(f"Starting getVendorByLogoMatch at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
            result=getVendorByLogoMatch(data,[response_layer.vendor_name],vendor_list,config_name,general_entity_fields)
            end_time = time.time()
            print(f"Completed getVendorByLogoMatch in {end_time - start_time:.4f} seconds")
            result.sort(key = lambda x: x[1],reverse=True)

            #sort results by secound column
            # print("vendor results are ")
            # print(result)
            if len(result)>0:
                return result[0][0],result[0][1]
            else:
                #return match_item,confidence
                if match_item is not None:
                    start_time = time.time()
                    print(f"Starting findfullyqualifiedcompanyname at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
                    name=findfullyqualifiedcompanyname(data,match_item,vendor_list,general_entity_fields,config_name)
                    end_time = time.time()
                    print(f"Completed findfullyqualifiedcompanyname in {end_time - start_time:.4f} seconds")

                    return name,83
                else:
                    #name=response_layer.vendor_name

                    start_time = time.time()
                    print(f"Starting findfullyqualifiedcompanyname at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
                    name=findfullyqualifiedcompanyname(data,response_layer.vendor_name,vendor_list,general_entity_fields,config_name)
                    end_time = time.time()
                    print(f"Completed findfullyqualifiedcompanyname in {end_time - start_time:.4f} seconds")
                    start_time = time.time()
                    print(f"Starting getfuzz_score_list at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
                    fuzz_result=getfuzz_score_list(companylist,name)
                    end_time = time.time()
                    print(f"Completed getfuzz_score_list in {end_time - start_time:.4f} seconds")
                    #if match is found it means vendname is present in block list
                    if fuzz_result is None:
                        return name,89
                        
    
    if p_res is not None:
        box=p_res[0]
        confidence=p_res[1]

        #extract all the text from bounding box
        start_time = time.time()
        print(f"Starting getTextFromBox at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
        boxtext = getTextFromBox(data,box,im_width,im_height)
        end_time = time.time()
        print(f"Completed getTextFromBox in {end_time - start_time:.4f} seconds")

        # print("boxtext")
        # print(boxtext)

        #check if any text matches the company format
        match_item=None
        if len(boxtext)>0:
            start_time = time.time()
            print(f"Starting getVendorbyMatch at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
            match_item=getVendorbyMatch(boxtext,companylist,config_name,general_entity_fields)
            end_time = time.time()
            print(f"Completed getVendorbyMatch in {end_time - start_time:.4f} seconds")
            # print("get vendr by match ",match_item)

        #if match_item is not None:
        #    return match_item,confidence

        #Search for Match in whole of the Invoice except the Company Name

        start_time = time.time()
        print(f"Starting getVendorByLogoMatch at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
        result=getVendorByLogoMatch(data,boxtext,vendor_list,config_name,general_entity_fields)
        end_time = time.time()
        print(f"Completed getVendorByLogoMatch in {end_time - start_time:.4f} seconds")
        result.sort(key = lambda x: x[1],reverse=True)

        #sort results by secound column
        # print("vendor results are ")
        # print(result)
        if len(result)>0:
            return result[0][0],result[0][1]
        else:
            if match_item is not None:
                start_time = time.time()
                print(f"Starting findfullyqualifiedcompanyname at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
                name=findfullyqualifiedcompanyname(data,match_item,vendor_list,general_entity_fields,config_name)
                end_time = time.time()
                print(f"Completed findfullyqualifiedcompanyname in {end_time - start_time:.4f} seconds")

                return name,confidence
            else:
                if len(boxtext)>0:
                    #if len(boxtext)>2:
                    #    boxtext=boxtext[0:1]
                    #s = ' '.join([str(n) for n in boxtext])
                    # print("boxtext ",boxtext[0])
                    start_time = time.time()
                    print(f"Starting getfuzz_score_list at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
                    fuzz_result=getfuzz_score_list(vendor_list,boxtext[0])
                    end_time = time.time()
                    print(f"Completed getfuzz_score_list in {end_time - start_time:.4f} seconds")
                    print("fuzz result",fuzz_result)
                    if fuzz_result is None:
                        start_time = time.time()
                        print(f"Starting findfullyqualifiedcompanyname at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
                        name=findfullyqualifiedcompanyname(data,boxtext[0],vendor_list,general_entity_fields,config_name)
                        end_time = time.time()
                        print(f"Completed findfullyqualifiedcompanyname in {end_time - start_time:.4f} seconds")
                        return name,66
    
    #Search vendor by email id or url :
    start_time = time.time()
    print(f"Starting getVendorByMail at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
    resbymail=getVendorByMail(data,vendor_list)
    end_time = time.time()
    print(f"Completed getVendorByMail in {end_time - start_time:.4f} seconds")
    # print("vendor results by email ",resbymail)
    if resbymail is not None:
        start_time = time.time()
        print(f"Starting getVendorByLogoMatch at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
        result=getVendorByLogoMatch(data,[resbymail],vendor_list,config_name,general_entity_fields)
        end_time = time.time()
        print(f"Completed getVendorByLogoMatch in {end_time - start_time:.4f} seconds")
        result.sort(key = lambda x: x[1],reverse=True)
        if len(result)>0:
            return result[0][0],result[0][1]
        else:
            return resbymail,100

    if len(boxtext)>0:
        start_time = time.time()
        print(f"Starting getfuzz_score_list at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
        fuzz_check_result = getfuzz_score_list(vendor_list,boxtext[0])
        end_time = time.time()
        print(f"Completed getfuzz_score_list in {end_time - start_time:.4f} seconds")
        if fuzz_check_result is None:
            return boxtext[0],confidence
    else:
        start_time = time.time()
        print(f"Starting getVendorByTop at {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
        match_item=getVendorByTop(data,im_height,vendor_list,config_name,general_entity_fields)
        end_time = time.time()
        print(f"Completed getVendorByTop in {end_time - start_time:.4f} seconds")
        if match_item is not None:
            return match_item[0],100
    return None,0