import re
import traceback
import tldextract
from fuzzywuzzy import fuzz
from PIL import Image
from spacy.matcher import PhraseMatcher
from Invoice.ML_Vendor_Extraction import *
from Invoice.DbConfig import *

#extract object initiated 
#extract = tldextract.TLDExtratct()

def getTextFromBox(data,box,im_width,im_height):

    # f = open(json_path,) 
    # data = json.load(f)
    
    x1= box[0] -50
    x2= box[1] + 100
    y1= box[2] 
    y2= box[3]
    print("get text from box ",y1,x1,y2,x2)
    text=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":

            add=d["Geometry"]["Polygon"]

             
            y1_= (add[0]["Y"]*im_height)
            y2_= (add[3]["Y"]*im_height)
            x1_= (add[0]["X"]*im_width)
            x2_= (add[2]["X"]*im_width)
            
            #print(d["Text"],"--",y1_,x1_,y2_,x2_)
            
            if y1_>y1 and y2_<y2 and x1_>x1 and x2_<x2:
                # print("ex text ",d["Text"])
                if len(d["Text"])>=3:
                    text.append(d["Text"])
    print("text from box ",text)
    return text 

def getVendorByTop(data,im_height,vendorlist,config_name,general_entity_fields):

    # f = open(json_path) 
    # data = json.load(f)

    lines=[]
    for d in data["Blocks"]:
        if d["BlockType"]=="LINE":
            top=d["Geometry"]["BoundingBox"]["Top"]
            lines.append( (d["Text"],top) )
            if top>(im_height*30):
                break
    
    f=[]
    for l in lines:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(l[0]))
        r=spacy_getorg(l[0],config_name,general_entity_fields)
        
        #if vendor list is from blocked content skip this vendor name
        if getfuzz_score_list(vendorlist,l[0]) is not None:
            continue
                        
        if len(r)>0:
            f.append(l)
            #print(l)
    print("all mathched results for vendors ",f)
    if len(f)>0:        
        f.sort(key = lambda x: x[1])
        return f[0]
    else:
        return None

def spacy_getorg(text, config_name, gen_fields):
    """
    Optimized organization extraction using spaCy with caching.

    Args:
        text: Text to extract organizations from
        config_name: Configuration name
        gen_fields: General entity fields

    Returns:
        list: List of organization names found
    """
    if not text or not text.strip():
        return []

    res = []

    # Extract ORG entities efficiently
    org_entities = [
        item['entity'] for item in gen_fields
        if item.get('fieldtype') == "ORG" and item.get('entity')
    ]

    # Process with spaCy
    doc = nlp(text)

    # Get standard ORG entities
    for ent in doc.ents:
        if ent.label_ == "ORG" and ent.text.strip():
            res.append(ent.text.strip())

    # Add phrase matcher results if we have custom entities
    if org_entities:
        phrase_matcher = PhraseMatcher(nlp.vocab)
        patterns = [nlp(entity) for entity in org_entities if entity.strip()]

        if patterns:
            phrase_matcher.add("ENTITY_PATTERN", patterns)
            matches = phrase_matcher(doc)

            for _, start, end in matches:
                span_text = doc[start:end].text.strip()
                if span_text and span_text not in res:
                    res.append(span_text)

    # Remove duplicates while preserving order
    seen = set()
    unique_res = []
    for item in res:
        if item not in seen:
            seen.add(item)
            unique_res.append(item)

    return unique_res

def spacy_geturl(text):
    urls=[]
    doc=nlp(text)
    for e in doc:
        if e.like_url:
            urls.append(e)
    
    return urls
 
def getVendorbyMatch(boxtext,company_list,config_name,general_entity_fields):
    
    #print("general entities getvendorbymatch ",general_entity_fields)
    f=[]
    #s = ' '.join([str(n) for n in boxtext])
    #boxtext=[s]

    for l in boxtext:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(l))
        r=spacy_getorg(l,config_name,general_entity_fields)
        # print("vendor ",r)
        if len(r)>0:
            #exclusion rule for removing block lists from vendor names
            fuzz_result=getfuzz_score_list(company_list,r[0])
            if fuzz_result is not None:
                continue
            f.append(r[0])
            # print("ven ext spacy ",r)
    # print("results of f ",f)
    if len(f)>0:
        return f[0]
    else:
        return None

def get_fuzz_score(str1, str2):
    partial_ratio = fuzz.partial_ratio(str1.lower(), str2.lower())
    return partial_ratio

def getfuzz_score_list(arr, str1, threshold=85):
    """
    Optimized fuzzy string matching with early termination.

    Args:
        arr: List of strings to match against
        str1: String to find matches for
        threshold: Minimum similarity score (default: 85)

    Returns:
        str: First matching string or None if no match found
    """
    if not arr or not str1:
        return None

    str1_lower = str1.lower()

    # Early termination on exact match
    for vendor in arr:
        if not vendor:
            continue

        vendor_lower = vendor.lower()

        # Check exact match first (fastest)
        if vendor_lower == str1_lower:
            return vendor

        # Then check fuzzy match
        ratio = fuzz.ratio(vendor_lower, str1_lower)
        if ratio > threshold:
            return vendor

    return None

def getVendorByMail(data, vendorlist):
    """
    Optimized vendor extraction from email addresses and URLs.

    Args:
        data: OCR data containing text blocks
        vendorlist: List of vendors to exclude

    Returns:
        str: Domain name from email or URL, or None if not found
    """
    if not data or not data.get("Blocks"):
        return None

    email_domains = []
    url_domains = []

    # Compile regex pattern once for better performance
    email_pattern = re.compile(r"[a-z0-9\.\-+_]+@[a-z0-9\.\-+_]+\.[a-z]+", re.IGNORECASE)

    for block in data["Blocks"]:
        if block.get("BlockType") != "LINE" or not block.get("Text"):
            continue

        text = block["Text"]

        # Extract emails
        emails = email_pattern.findall(text.lower())
        for email in emails:
            if getfuzz_score_list(vendorlist, email) is None:
                try:
                    domain = email.split("@")[1].split(".")[0]
                    if domain and domain not in email_domains:
                        email_domains.append(domain)
                except (IndexError, AttributeError):
                    continue

        # Extract URLs
        urls = spacy_geturl(text)
        for url in urls:
            try:
                extracted = tldextract.extract(str(url))
                domain = str(extracted.domain)

                if domain and getfuzz_score_list(vendorlist, domain) is None:
                    if domain not in url_domains:
                        url_domains.append(domain)
            except Exception:
                continue

    # Filter out empty strings and return first valid result
    url_domains = [x for x in url_domains if x.strip()]
    email_domains = [x for x in email_domains if x.strip()]

    # Prioritize URL domains over email domains
    if url_domains:
        return url_domains[0]

    if email_domains:
        return email_domains[0]

    return None

def findfullyqualifiedcompanyname(data,vendorname,vendorlist,general_entity_fields,config_name):
    text_data=[]
    # with open(json_path) as json_file:

    #     data=json.load(json_file)

    for blocks in data['Blocks']:
        if (blocks["BlockType"]=='LINE'):
            text_data.append(blocks["Text"])
    
    company_list=[]
    for i in text_data:
        #r=list(lexnlp.extract.en.entities.nltk_re.get_companies(i))
        r=spacy_getorg(i,config_name,general_entity_fields)

        if len(r)>0 :
            if getfuzz_score_list(vendorlist,r[0]) is None:
                company_list.append(r[0])
    res=[]
    for i in company_list:
        score=get_fuzz_score(i, vendorname)
        if score>90:
            res.append((i,score))
    
    res.sort(key = lambda x: x[1],reverse=True)
    if len(res)>0:
        return res[0][0]
    else:
        return vendorname

def getVendorByLogoMatch(data, boxtext, vendorlist, config_name, general_entity_fields):
    """
    Optimized vendor matching by logo with improved performance.

    Args:
        data: OCR data containing text blocks
        boxtext: List of text from bounding box
        vendorlist: List of vendors to exclude
        config_name: Configuration name
        general_entity_fields: General entity fields for matching

    Returns:
        list: List of [vendor_name, score] tuples
    """
    print("IN LOGO MATCH")

    if not boxtext:
        return []

    # Extract all line text efficiently
    text_data = [
        block["Text"] for block in data.get('Blocks', [])
        if block.get("BlockType") == 'LINE' and block.get("Text")
    ]

    if not text_data:
        return []

    print(f"Processing {len(text_data)} text lines")

    # Extract company names with filtering
    company_list = []
    for text_line in text_data:
        organizations = spacy_getorg(text_line, config_name, general_entity_fields)

        for org in organizations:
            if org and getfuzz_score_list(vendorlist, org) is None:
                company_list.append(org)

    if not company_list:
        return []

    # Optimize matching with early termination and better scoring
    result = []
    min_text_length = 3
    score_threshold = 85

    for company in company_list:
        best_match = None
        best_score = 0

        for box_text in boxtext:
            if len(box_text) < min_text_length:
                continue

            score = fuzz.partial_ratio(company.lower(), box_text.lower())

            if score >= score_threshold and score > best_score:
                best_score = score
                best_match = company

        if best_match:
            result.append([best_match, best_score])

    # Remove duplicates and sort by score
    seen = set()
    unique_result = []
    for item in result:
        if item[0] not in seen:
            seen.add(item[0])
            unique_result.append(item)

    return unique_result

# global_config=None

# Cache for general entity fields to avoid repeated database calls
_general_entity_cache = {}

def clear_general_entity_cache():
    """Clear the general entity cache. Useful for testing or when configuration changes."""
    global _general_entity_cache
    _general_entity_cache.clear()

def _get_cached_general_entity(config_name):
    """Get general entity fields with caching to avoid repeated database calls."""
    if config_name not in _general_entity_cache:
        _general_entity_cache[config_name] = get_General_entity(config_name)
    return _general_entity_cache[config_name]

def _validate_inputs(model, image, data, config_name):
    """Validate input parameters."""
    if not model:
        raise ValueError("Model parameter is required")
    if not image:
        raise ValueError("Image parameter is required")
    if not data:
        raise ValueError("Data parameter is required")
    if not config_name:
        raise ValueError("Config name parameter is required")

def _get_image_dimensions(image_path):
    """Get image dimensions with error handling."""
    try:
        with Image.open(image_path) as im:
            return im.size
    except Exception as e:
        print(f"Error opening image {image_path}: {e}")
        raise

def _process_ml_prediction(model, image, im_width, im_height):
    """Process ML model prediction with proper error handling."""
    try:
        return predict(model, image, im_width, im_height)
    except Exception as e:
        print(f"Error in ML Vendor Extraction: {e}")
        print(traceback.format_exc())
        return None

def _process_response_layer_vendor(response_layer, images_list, data, vendor_list,
                                 config_name, general_entity_fields, companylist):
    """Process vendor extraction from response layer."""
    if not response_layer.executed:
        response_layer.execute(images_list)

    if not (response_layer.vendor_name and len(response_layer.vendor_name) > 0):
        return None, 0

    print("vendor name API 2", response_layer.vendor_name)

    # Try to match vendor name
    match_item = getVendorbyMatch([response_layer.vendor_name], companylist,
                                config_name, general_entity_fields)
    print("get vendor by match API2", match_item)

    # Get logo match results
    result = getVendorByLogoMatch(data, [response_layer.vendor_name], vendor_list,
                                config_name, general_entity_fields)

    if result:
        result.sort(key=lambda x: x[1], reverse=True)
        return result[0][0], result[0][1]

    # Fallback to match item or fully qualified name
    if match_item is not None:
        name = findfullyqualifiedcompanyname(data, match_item, vendor_list,
                                           general_entity_fields, config_name)
        return name, 83

    # Final fallback
    name = findfullyqualifiedcompanyname(data, response_layer.vendor_name, vendor_list,
                                       general_entity_fields, config_name)
    fuzz_result = getfuzz_score_list(companylist, name)
    if fuzz_result is None:
        return name, 89

    return None, 0

def _process_ml_result(p_res, data, im_width, im_height, companylist, vendor_list,
                      config_name, general_entity_fields):
    """Process ML prediction result."""
    box, confidence = p_res

    # Extract text from bounding box
    boxtext = getTextFromBox(data, box, im_width, im_height)

    if not boxtext:
        return None, 0, boxtext

    # Check if any text matches company format
    match_item = getVendorbyMatch(boxtext, companylist, config_name, general_entity_fields)

    # Search for match in whole invoice
    result = getVendorByLogoMatch(data, boxtext, vendor_list, config_name, general_entity_fields)

    if result:
        result.sort(key=lambda x: x[1], reverse=True)
        return result[0][0], result[0][1], boxtext

    # Fallback to match item
    if match_item is not None:
        name = findfullyqualifiedcompanyname(data, match_item, vendor_list,
                                           general_entity_fields, config_name)
        return name, confidence, boxtext

    # Final fallback with boxtext
    if boxtext:
        fuzz_result = getfuzz_score_list(vendor_list, boxtext[0])
        print("fuzz result", fuzz_result)
        if fuzz_result is None:
            name = findfullyqualifiedcompanyname(data, boxtext[0], vendor_list,
                                               general_entity_fields, config_name)
            return name, 66, boxtext

    return None, 0, boxtext

def _process_email_vendor(data, vendor_list, config_name, general_entity_fields):
    """Process vendor extraction from email/URL."""
    resbymail = getVendorByMail(data, vendor_list)

    if resbymail is None:
        return None, 0

    result = getVendorByLogoMatch(data, [resbymail], vendor_list, config_name, general_entity_fields)

    if result:
        result.sort(key=lambda x: x[1], reverse=True)
        return result[0][0], result[0][1]

    return resbymail, 100

def getVendors(model, image, data, companylist, config_name, response_layer, images_list):
    """
    Optimized vendor extraction function with improved structure and performance.

    Args:
        model: ML model for vendor detection
        image: Path to image file
        data: OCR data (AWS Textract format)
        companylist: List of company names to exclude
        config_name: Configuration name
        response_layer: Response layer object
        images_list: List of images for processing

    Returns:
        tuple: (vendor_name, confidence_score)
    """
    # Input validation
    _validate_inputs(model, image, data, config_name)

    # Get cached general entity fields
    general_entity_fields = _get_cached_general_entity(config_name)

    # Prepare vendor list
    vendor_list = companylist if companylist is not None else []

    # Get image dimensions (lazy loading - only when needed)
    im_width, im_height = _get_image_dimensions(image)

    # Try ML model prediction first
    p_res = _process_ml_prediction(model, image, im_width, im_height)

    # Process ML result if available
    if p_res is not None:
        vendor_name, confidence, boxtext = _process_ml_result(
            p_res, data, im_width, im_height, companylist, vendor_list,
            config_name, general_entity_fields
        )
        if vendor_name:
            return vendor_name, confidence
    else:
        boxtext = []

    # Fallback to response layer if ML failed
    vendor_name, confidence = _process_response_layer_vendor(
        response_layer, images_list, data, vendor_list,
        config_name, general_entity_fields, companylist
    )
    if vendor_name:
        return vendor_name, confidence

    # Try email/URL extraction
    vendor_name, confidence = _process_email_vendor(data, vendor_list, config_name, general_entity_fields)
    if vendor_name:
        return vendor_name, confidence

    # Final fallback: use boxtext or top vendor
    if boxtext and getfuzz_score_list(vendor_list, boxtext[0]) is None:
        return boxtext[0], p_res[1] if p_res else 0

    # Last resort: get vendor by top position
    match_item = getVendorByTop(data, im_height, vendor_list, config_name, general_entity_fields)
    if match_item is not None:
        return match_item[0], 100

    return None, 0