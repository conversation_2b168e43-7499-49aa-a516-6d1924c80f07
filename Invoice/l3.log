[14/Jun/2025 11:17:39] "POST /api/token/ HTTP/1.1" 200 438
********
Time taken to read request body:  6.504039764404297
Time taken to check config name:  0.21990036964416504
Time taken to log to db:  2.271207809448242
Time taken to get max page count:  0.4416344165802002
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Failed to create PNG file.. Trying to check file type from BASE64...
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Identified file type is PDF...
Page 1: width=1785.0, height=2526.0
Page 2: width=1785.0, height=2526.0
Page 3: width=1785.0, height=2526.0
Page 4: width=1785.0, height=2526.0
Page 5: width=1785.0, height=2526.0
Page 6: width=1785.0, height=2526.0
Page 7: width=1785.0, height=2526.0
Page 8: width=1785.0, height=2526.0
Page 9: width=1785.0, height=2526.0
Page 10: width=1785.0, height=2526.0
Page 11: width=1785.0, height=2526.0
Page 12: width=1785.0, height=2526.0
Page 13: width=1785.0, height=2526.0
Page 14: width=1785.0, height=2526.0
Page 15: width=1785.0, height=2526.0
Page 16: width=1785.0, height=2526.0
Page 17: width=1785.0, height=2526.0
Page 18: width=1785.0, height=2526.0
Page 19: width=1785.0, height=2526.0
Page 20: width=1785.0, height=2526.0
Page 21: width=1785.0, height=2526.0
Page 22: width=1785.0, height=2526.0
Page 23: width=1785.0, height=2526.0
Page 24: width=1785.0, height=2526.0
Page 25: width=1785.0, height=2526.0
Page 26: width=1785.0, height=2526.0
Page 27: width=1785.0, height=2526.0
Page 28: width=1785.0, height=2526.0
Page 29: width=1785.0, height=2526.0
Time taken to get page size:  0.017397403717041016
c90aff3f-3200-4f0b-970e-c0afa6faca22 - images count:10max_pagecount :10
Time taken to update page count:  0.5961740016937256
Time taken to predict invoice:  1.5027685165405273
Time taken to predict invoice:  1.802884578704834
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Invoice -Invoice Ml Prediction 
c90aff3f-3200-4f0b-970e-c0afa6faca22 -  ##################################### 1 ################### {'NI': 0.5787813663482666, 'I': 0.4212186634540558}
Time taken to read image:  0.017061233520507812
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES', 'FORMS']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  10.784359693527222
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...


== FOUND KEY : VALUE pairs ===

Time taken to get raw values:  0.0008149147033691406
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_0.png
Time taken to mark headers:  2.722097396850586
Starting getlineitems at 2025-06-14 11:18:44
Starting ML prediction at 2025-06-14 11:18:44
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_0-markup.png
Completed ML prediction in 5.5960 seconds
Starting output processing at 2025-06-14 11:18:50
Completed output processing in 0.0005 seconds
Starting overlap detection at 2025-06-14 11:18:50
Completed overlap detection in 0.0003 seconds
Starting cell mapping at 2025-06-14 11:18:50
Completed cell mapping in 0.0015 seconds
Starting image processing at 2025-06-14 11:18:50
Completed image processing in 0.0014 seconds
Starting bounds calculation at 2025-06-14 11:18:50
Completed bounds calculation in 0.0006 seconds
Starting table extraction at 2025-06-14 11:18:50
Completed table extraction in 0.0041 seconds
Starting header detection at 2025-06-14 11:18:50
checking header exists
Completed header detection in 2.3115 seconds
Total getlineitems execution time: 8.7339 seconds
Time taken to get line items:  8.736895561218262
Time taken to get height:  0.005277156829833984
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  1.653454303741455
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  1000012398
regex match  2022SIUS0174118
regex match  1000012398
regex match  2022SIUS0174118
regex match  1000012398
regex match  836
regex match  365
regex match  365
regex match  1000012398
regex match  365
regex match  62-1644402
word list 
['date ', 'date ']
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  18.08342671394348
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  18.096767902374268
Time taken to process line items:  3.337860107421875e-06
Time taken to predict invoice:  1.5792200565338135
Time taken to predict invoice:  1.867077112197876
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Invoice -Invoice Ml Prediction 
c90aff3f-3200-4f0b-970e-c0afa6faca22 -  ##################################### 2 ################### {'NI': 0.4693775177001953, 'I': 0.5306224226951599}
Time taken to read image:  0.004016399383544922
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  6.531010150909424
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_1.png
Time taken to mark headers:  2.8941104412078857
Starting getlineitems at 2025-06-14 11:19:26
Starting ML prediction at 2025-06-14 11:19:26
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_1-markup.png
Completed ML prediction in 5.8807 seconds
Starting output processing at 2025-06-14 11:19:32
Completed output processing in 0.0006 seconds
Starting overlap detection at 2025-06-14 11:19:32
Completed overlap detection in 0.0002 seconds
Starting cell mapping at 2025-06-14 11:19:32
Completed cell mapping in 0.0002 seconds
Starting image processing at 2025-06-14 11:19:32
Completed image processing in 0.0008 seconds
No final rows found, returning None
Time taken to get line items:  6.807995796203613
Time taken to get height:  0.0016674995422363281
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  1.9322938919067383
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  18
regex match  23
regex match  62-1644402
word list 
['date ', 'date ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  22.52132749557495
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  22.534326314926147
Time taken to process line items:  4.0531158447265625e-06
Time taken to predict invoice:  1.5539140701293945
Time taken to predict invoice:  1.8193926811218262
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Invoice -Invoice Ml Prediction 
c90aff3f-3200-4f0b-970e-c0afa6faca22 -  ##################################### 3 ################### {'NI': 0.48491421341896057, 'I': 0.5150858163833618}
Time taken to read image:  0.005753755569458008
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  10.129213809967041
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_2.png
Time taken to mark headers:  2.8317108154296875
Starting getlineitems at 2025-06-14 11:20:13
Starting ML prediction at 2025-06-14 11:20:13
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_2-markup.png
Completed ML prediction in 5.5360 seconds
Starting output processing at 2025-06-14 11:20:19
Completed output processing in 0.0005 seconds
Starting overlap detection at 2025-06-14 11:20:19
Completed overlap detection in 0.0004 seconds
Starting cell mapping at 2025-06-14 11:20:19
Completed cell mapping in 0.0016 seconds
Starting image processing at 2025-06-14 11:20:19
Completed image processing in 0.0011 seconds
Starting bounds calculation at 2025-06-14 11:20:19
Completed bounds calculation in 0.0005 seconds
Starting table extraction at 2025-06-14 11:20:19
Completed table extraction in 0.0036 seconds
Starting header detection at 2025-06-14 11:20:19
checking header exists
Completed header detection in 2.1987 seconds
Total getlineitems execution time: 8.5690 seconds
Time taken to get line items:  8.569800853729248
Time taken to get height:  0.0008203983306884766
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  2.0136945247650146
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  62-1644402
word list 
['date ', 'date ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  19.77809977531433
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  19.792349815368652
Time taken to process line items:  4.76837158203125e-06
Time taken to predict invoice:  1.5688395500183105
Time taken to predict invoice:  1.8428089618682861
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Invoice -Invoice Ml Prediction 
c90aff3f-3200-4f0b-970e-c0afa6faca22 -  ##################################### 4 ################### {'NI': 0.4515429139137268, 'I': 0.5484570860862732}
Time taken to read image:  0.006400108337402344
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  9.076760292053223
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_3.png
Time taken to mark headers:  2.6812665462493896
Starting getlineitems at 2025-06-14 11:20:59
Starting ML prediction at 2025-06-14 11:20:59
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_3-markup.png
Completed ML prediction in 6.0413 seconds
Starting output processing at 2025-06-14 11:21:05
Completed output processing in 0.0005 seconds
Starting overlap detection at 2025-06-14 11:21:05
Completed overlap detection in 0.0002 seconds
Starting cell mapping at 2025-06-14 11:21:05
Completed cell mapping in 0.0002 seconds
Starting image processing at 2025-06-14 11:21:05
Completed image processing in 0.0010 seconds
No final rows found, returning None
Time taken to get line items:  6.959301710128784
Time taken to get height:  0.014698028564453125
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  1.9743797779083252
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  1423070
regex match  365
regex match  62-1644402
word list 
['date ', 'date ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  21.04889440536499
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  21.062342882156372
Time taken to process line items:  4.291534423828125e-06
Time taken to predict invoice:  1.544504165649414
Time taken to predict invoice:  1.8433074951171875
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Invoice -Invoice Ml Prediction 
c90aff3f-3200-4f0b-970e-c0afa6faca22 -  ##################################### 5 ################### {'NI': 0.45489737391471863, 'I': 0.545102596282959}
Time taken to read image:  0.01638174057006836
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  15.14144492149353
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_4.png
Time taken to mark headers:  2.5273873805999756
Starting getlineitems at 2025-06-14 11:21:50
Starting ML prediction at 2025-06-14 11:21:50
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_4-markup.png
Completed ML prediction in 5.5584 seconds
Starting output processing at 2025-06-14 11:21:56
Completed output processing in 0.0006 seconds
Starting overlap detection at 2025-06-14 11:21:56
Completed overlap detection in 0.0004 seconds
Starting cell mapping at 2025-06-14 11:21:56
Completed cell mapping in 0.0012 seconds
Starting image processing at 2025-06-14 11:21:56
Completed image processing in 0.0016 seconds
Starting bounds calculation at 2025-06-14 11:21:56
Completed bounds calculation in 0.0032 seconds
Starting table extraction at 2025-06-14 11:21:56
Completed table extraction in 0.0033 seconds
Starting header detection at 2025-06-14 11:21:56
checking header exists
Completed header detection in 2.1418 seconds
Total getlineitems execution time: 8.5512 seconds
Time taken to get line items:  8.552964210510254
Time taken to get height:  0.0006377696990966797
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  1.8060271739959717
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  62-1644402
word list 
['date ', 'date ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  20.39609146118164
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  20.41033387184143
Time taken to process line items:  4.5299530029296875e-06
Time taken to predict invoice:  1.5806858539581299
Time taken to predict invoice:  1.8557186126708984
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Invoice -Invoice Ml Prediction 
c90aff3f-3200-4f0b-970e-c0afa6faca22 -  ##################################### 6 ################### {'NI': 0.5285399556159973, 'I': 0.4714600741863251}
Time taken to read image:  0.02129983901977539
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  10.27861213684082
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_5.png
Time taken to mark headers:  3.1959638595581055
Starting getlineitems at 2025-06-14 11:22:38
Starting ML prediction at 2025-06-14 11:22:38
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_5-markup.png
Completed ML prediction in 5.5090 seconds
Starting output processing at 2025-06-14 11:22:43
Completed output processing in 0.0005 seconds
Starting overlap detection at 2025-06-14 11:22:43
Completed overlap detection in 0.0003 seconds
Starting cell mapping at 2025-06-14 11:22:43
Completed cell mapping in 0.0023 seconds
Starting image processing at 2025-06-14 11:22:43
Completed image processing in 0.0009 seconds
Starting bounds calculation at 2025-06-14 11:22:43
Completed bounds calculation in 0.0006 seconds
Starting table extraction at 2025-06-14 11:22:43
Completed table extraction in 0.0042 seconds
Starting header detection at 2025-06-14 11:22:43
checking header exists
Completed header detection in 1.8521 seconds
Total getlineitems execution time: 8.2078 seconds
Time taken to get line items:  8.208876848220825
Time taken to get height:  0.0009341239929199219
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  1.7276861667633057
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  365
regex match  62-1644402
word list 
['date ', 'date ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  21.087498664855957
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  21.10050344467163
Time taken to process line items:  5.9604644775390625e-06
Time taken to predict invoice:  1.6313228607177734
Time taken to predict invoice:  1.7923460006713867
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Invoice -Invoice Ml Prediction 
c90aff3f-3200-4f0b-970e-c0afa6faca22 -  ##################################### 7 ################### {'NI': 0.5483431220054626, 'I': 0.45165690779685974}
Time taken to read image:  0.0007975101470947266
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  7.984697341918945
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_6.png
Time taken to mark headers:  3.375606060028076
Starting getlineitems at 2025-06-14 11:23:24
Starting ML prediction at 2025-06-14 11:23:24
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_6-markup.png
Completed ML prediction in 5.9491 seconds
Starting output processing at 2025-06-14 11:23:30
Completed output processing in 0.0006 seconds
Starting overlap detection at 2025-06-14 11:23:30
Completed overlap detection in 0.0004 seconds
Starting cell mapping at 2025-06-14 11:23:30
Completed cell mapping in 0.0003 seconds
Starting image processing at 2025-06-14 11:23:30
Completed image processing in 0.0012 seconds
No final rows found, returning None
Time taken to get line items:  6.898104429244995
Time taken to get height:  0.014306783676147461
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  1.8923594951629639
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  365
regex match  62-1644402
word list 
['date ', 'date ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  19.723262071609497
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  19.73660945892334
Time taken to process line items:  4.0531158447265625e-06
Time taken to predict invoice:  1.5757663249969482
Time taken to predict invoice:  1.8402044773101807
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Invoice -Invoice Ml Prediction 
c90aff3f-3200-4f0b-970e-c0afa6faca22 -  ##################################### 8 ################### {'NI': 0.4661165475845337, 'I': 0.5338834524154663}
Time taken to read image:  0.02235698699951172
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  16.92021608352661
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_7.png
Time taken to mark headers:  55.214717626571655
Starting getlineitems at 2025-06-14 11:25:08
Starting ML prediction at 2025-06-14 11:25:08
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_7-markup.png
Completed ML prediction in 6.4057 seconds
Starting output processing at 2025-06-14 11:25:14
Completed output processing in 0.0006 seconds
Starting overlap detection at 2025-06-14 11:25:14
Completed overlap detection in 0.0005 seconds
Starting cell mapping at 2025-06-14 11:25:14
Completed cell mapping in 0.0025 seconds
Starting image processing at 2025-06-14 11:25:14
Completed image processing in 0.0018 seconds
Starting bounds calculation at 2025-06-14 11:25:14
Completed bounds calculation in 0.0005 seconds
Starting table extraction at 2025-06-14 11:25:14
Completed table extraction in 0.0038 seconds
Starting header detection at 2025-06-14 11:25:14
checking header exists
Completed header detection in 1.5655 seconds
Total getlineitems execution time: 8.8678 seconds
Time taken to get line items:  8.868336915969849
Time taken to get height:  0.0006115436553955078
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  1.6204211711883545
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  62-1644402
word list 
['date ', 'date ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  32.122546672821045
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  32.13722038269043
Time taken to process line items:  4.76837158203125e-06
Time taken to predict invoice:  1.635228157043457
Time taken to predict invoice:  1.8248615264892578
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Invoice -Invoice Ml Prediction 
c90aff3f-3200-4f0b-970e-c0afa6faca22 -  ##################################### 9 ################### {'NI': 0.4319807291030884, 'I': 0.5680192708969116}
Time taken to read image:  0.027070283889770508
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES', 'FORMS']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  11.141049861907959
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...


== FOUND KEY : VALUE pairs ===

Time taken to get raw values:  0.0006988048553466797
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_8.png
Time taken to mark headers:  2.5389928817749023
Starting getlineitems at 2025-06-14 11:26:08
Starting ML prediction at 2025-06-14 11:26:08
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_8-markup.png
Completed ML prediction in 5.7211 seconds
Starting output processing at 2025-06-14 11:26:13
Completed output processing in 0.0005 seconds
Starting overlap detection at 2025-06-14 11:26:13
Completed overlap detection in 0.0004 seconds
Starting cell mapping at 2025-06-14 11:26:13
Completed cell mapping in 0.0010 seconds
Starting image processing at 2025-06-14 11:26:13
Completed image processing in 0.0011 seconds
Starting bounds calculation at 2025-06-14 11:26:13
Completed bounds calculation in 0.0005 seconds
Starting table extraction at 2025-06-14 11:26:13
Completed table extraction in 0.0029 seconds
Starting header detection at 2025-06-14 11:26:13
checking header exists
Completed header detection in 2.0979 seconds
Total getlineitems execution time: 8.6532 seconds
Time taken to get line items:  8.653873443603516
Time taken to get height:  0.009565353393554688
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  1.7584781646728516
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  62-1644402
word list 
['date ', 'date ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  21.091257572174072
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  21.106428861618042
Time taken to process line items:  3.814697265625e-06
Time taken to predict invoice:  1.5764634609222412
Time taken to read image:  0.020006895065307617
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Features Used ['TABLES', 'FORMS']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - fetching Boto Response .....
Time taken to get boto response:  7.9863669872283936
c90aff3f-3200-4f0b-970e-c0afa6faca22 - JSON File Written ...


== FOUND KEY : VALUE pairs ===

Time taken to get raw values:  0.0007202625274658203
c90aff3f-3200-4f0b-970e-c0afa6faca22 - marking for /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_9.png
Time taken to mark headers:  2.505723714828491
Starting getlineitems at 2025-06-14 11:26:51
Starting ML prediction at 2025-06-14 11:26:51
image_path  /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_9-markup.png
Completed ML prediction in 6.2029 seconds
Starting output processing at 2025-06-14 11:26:57
Completed output processing in 0.0010 seconds
Starting overlap detection at 2025-06-14 11:26:57
Completed overlap detection in 0.0003 seconds
Starting cell mapping at 2025-06-14 11:26:57
Completed cell mapping in 0.0012 seconds
Starting image processing at 2025-06-14 11:26:57
Completed image processing in 0.0011 seconds
Starting bounds calculation at 2025-06-14 11:26:57
Completed bounds calculation in 0.0007 seconds
Starting table extraction at 2025-06-14 11:26:57
Completed table extraction in 0.0029 seconds
Starting header detection at 2025-06-14 11:26:57
checking header exists
Completed header detection in 1.8857 seconds
Total getlineitems execution time: 8.9497 seconds
Time taken to get line items:  8.950063943862915
Time taken to get height:  0.0005910396575927734
Nearest Distance condition  [{'name': 'Invoice_No', 'type': 'Alpha', 'head': 'invoice,\r\ninvoice:,\r\nInvoice ID', 'tail': 'no,\r\nno.,\r\nid,\r\nno:,\r\nnumber,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'PO_no', 'type': 'Rule', 'head': 'po,\r\npurchase,\r\nP.O.,\r\nBill to PO,\r\nP O Number,', 'tail': 'no,\r\nnumber,\r\norder,\r\nPO#,\r\n#', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'Issue_Date', 'type': 'Date', 'head': 'issue,', 'tail': ',\r\ndate,', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'bank_name', 'type': 'Rule', 'head': 'bank,\r\nbank name', 'tail': 'name', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'bank'}, {'LOWER': 'of'}, {'OP': '?'}], 'two': [{'IS_ASCII': True}, {'LOWER': 'bank'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'account_number', 'type': 'Alpha', 'head': 'account', 'tail': 'number', 'strategy': 'fuzz', 'rule': None, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'terms', 'type': 'Alpha', 'head': 'terms', 'tail': ',', 'strategy': 'fuzz', 'rule': {'one': [{'LOWER': 'net'}, {'LIKE_NUM': True}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}, {'name': 'ship_to', 'type': 'Regex', 'head': 'ship to,\r\nship', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'bill_to', 'type': 'Regex', 'head': 'bill to,\r\nbill', 'tail': ',\r\nto', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'ORG'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': [{'id': 6, 'name': 'string', 'description': '', 'regex': '[a-zA-Z0-9].*', 'regex_index': None}]}, {'name': 'balance_due', 'type': 'Rule', 'head': 'balance', 'tail': 'due', 'strategy': 'fuzz', 'rule': {'one': [{'ENT_TYPE': 'MONEY'}]}, 'conditionfield': {}, 'hascondition': False, 'regex': None}]
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
extract field has condition False
extracted value  None 0
Time taken to get form fields by distance:  1.6679656505584717
In Linear Form Fields
word list 
['invoice ', 'invoice no', 'invoice no:', 'invoice no.', 'invoice id', 'invoice number', 'invoice: ', 'invoice: no', 'invoice: no:', 'invoice: no.', 'invoice: id', 'invoice: number', 'Invoice ID ', 'Invoice ID no', 'Invoice ID no:', 'Invoice ID no.', 'Invoice ID id', 'Invoice ID number', 'Invoice : ', 'Invoice : no', 'Invoice : no:', 'Invoice : no.', 'Invoice : id', 'Invoice : number', ' ', ' no', ' no:', ' no.', ' id', ' number']
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  2022SIUS0174118
regex match  62-1644402
word list 
['date ', 'date ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
word list 
['total EUR', 'total GBP', 'total NET', 'total ', 'total ', 'total amount EUR', 'total amount GBP', 'total amount NET', 'total amount ', 'total amount ', 'amount EUR', 'amount GBP', 'amount NET', 'amount ', 'amount ', 'all currency in EUR', 'all currency in GBP', 'all currency in NET', 'all currency in ', 'all currency in ', 'Per Hr Unit Rate EUR', 'Per Hr Unit Rate GBP', 'Per Hr Unit Rate NET', 'Per Hr Unit Rate ', 'Per Hr Unit Rate ', 'cur EUR', 'cur GBP', 'cur NET', 'cur ', 'cur ', 'Cur EUR', 'Cur GBP', 'Cur NET', 'Cur ', 'Cur ', 'GBP EUR', 'GBP GBP', 'GBP NET', 'GBP ', 'GBP ', ' EUR', ' GBP', ' NET', ' ', ' ', ' EUR', ' GBP', ' NET', ' ', ' ']
word list 
['total usd ', 'total usd ', ' ', ' ']
c90aff3f-3200-4f0b-970e-c0afa6faca22 - No values Found
Time taken to get form fields by linear:  19.385261058807373
c90aff3f-3200-4f0b-970e-c0afa6faca22 - extracted form fields 
Time taken to get confidence matrix:  19.399126768112183
Time taken to process line items:  5.245208740234375e-06
images response layer {0: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_0.png', 1: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_1.png', 2: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_2.png', 3: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_3.png', 4: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_4.png', 5: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_5.png', 6: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_6.png', 7: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_7.png', 8: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_8.png', 9: '/home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_9.png'}
Time taken by response layer 96.27498698234558
Time taken to execute response layer:  96.27746486663818
Time taken to get extraction list:  3.956620454788208
Time taken to create word list:  5.269050598144531e-05
Time taken to get match threshold:  0.8968367576599121
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Duplicate found {'Invoice_No': '2022SIUS0174118', 'confidence_level': 95} :for Invoice_No , Match_score :
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Duplicate found {'Invoice_No': '2022SIUS0174118', 'confidence_level': 94.1614761352539} :for Invoice_No , Match_score :
Time taken to append form fields:  0.07789349555969238
checklist info  {0: {'NI': 0.18776248395442963, 'I': 0.8122375011444092}, 1: {'NI': 0.5787813663482666, 'I': 0.4212186634540558}, 2: {'NI': 0.4693775177001953, 'I': 0.5306224226951599}, 3: {'NI': 0.48491421341896057, 'I': 0.5150858163833618}, 4: {'NI': 0.4515429139137268, 'I': 0.5484570860862732}, 5: {'NI': 0.45489737391471863, 'I': 0.545102596282959}, 6: {'NI': 0.5285399556159973, 'I': 0.4714600741863251}, 7: {'NI': 0.5483431220054626, 'I': 0.45165690779685974}, 8: {'NI': 0.4661165475845337, 'I': 0.5338834524154663}, 9: {'NI': 0.4319807291030884, 'I': 0.5680192708969116}}
Time taken to get company names:  0.4445514678955078
length of vendorfields  1
before sorting  []
after sorting  []
matched key  None
Time taken to filter vendor fields:  1.3688745498657227
c90aff3f-3200-4f0b-970e-c0afa6faca22 - Key Value Vendor name None
Time taken to detect document text response:  6.863840818405151
Starting get_General_entity at 2025-06-14 11:29:11
Completed get_General_entity in 12.1128 seconds
Starting Image.open at 2025-06-14 11:29:23
Completed Image.open in 0.0018 seconds
Starting predict at 2025-06-14 11:29:23
0 0.7596934
 index  0  conf :  0.7596934
final box [[3.2356843713205308, 498.78978833556175, 7.637440033257008, 244.71520087122917]]
Completed predict in 0.1686 seconds
Starting getTextFromBox at 2025-06-14 11:29:23
get text from box  7.637440033257008 -46.76431562867947 244.71520087122917 598.7897883355618
text from box  ['INCRAM', 'CLOUD', '3351 Michelson, Suite 100', 'Irvine CA 92612-0697']
Completed getTextFromBox in 0.0026 seconds
Starting getVendorbyMatch at 2025-06-14 11:29:23
Completed getVendorbyMatch in 0.0006 seconds
Starting getVendorByLogoMatch at 2025-06-14 11:29:23
IN LOGO MATCH
length of text data 255
Completed getVendorByLogoMatch in 0.0165 seconds
Starting getfuzz_score_list at 2025-06-14 11:29:23
Completed getfuzz_score_list in 0.0003 seconds
fuzz result None
Starting findfullyqualifiedcompanyname at 2025-06-14 11:29:23
Completed findfullyqualifiedcompanyname in 0.0006 seconds
c90aff3f-3200-4f0b-970e-c0afa6faca22 - ven res ('INCRAM', 66)
Time taken to get vendor name:  12.306272745132446
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_0invoice.json :  0.013420581817626953
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_0invoice.json :  1.6665065288543701
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_1invoice.json :  0.011857986450195312
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_1invoice.json :  0.6774470806121826
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_2invoice.json :  0.011389493942260742
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_2invoice.json :  0.6759185791015625
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_3invoice.json :  0.011079788208007812
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_3invoice.json :  1.5532126426696777
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_4invoice.json :  0.0105743408203125
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_4invoice.json :  1.9611287117004395
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_5invoice.json :  0.010713815689086914
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_5invoice.json :  0.8987066745758057
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_6invoice.json :  0.012216567993164062
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_6invoice.json :  0.7920620441436768
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_7invoice.json :  0.010254383087158203
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_7invoice.json :  0.6704995632171631
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_8invoice.json :  0.011792898178100586
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_8invoice.json :  0.6907641887664795
Nearest Distance condition  [{'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Validity, Inc.'}, 'hascondition': True, 'regex': [{'id': 12, 'name': 'invoice number starting with #', 'description': 'example :#INV378136', 'regex': '^(#)+[A-Z0-9]{7,12}$', 'regex_index': None}]}, {'name': 'invoice_no', 'type': 'Regex', 'head': 'invoice ,', 'tail': ',', 'strategy': '', 'rule': {}, 'conditionfield': {'vendor_name': 'Sidney Lee Welding Supply, Inc'}, 'hascondition': True, 'regex': [{'id': 13, 'name': 'invoice number numbers only ,9-12 range', 'description': '', 'regex': '[0-9]{9,12}', 'regex_index': None}]}, {'name': 'invoice_Date', 'type': 'Date', 'head': 'Invoice,', 'tail': ',', 'strategy': 'fuzz', 'rule': '', 'conditionfield': {'vendor_name': 'LOGISTICS'}, 'hascondition': True, 'regex': None}]
extract field has condition True
extract field has condition True
extract field has condition True
Time taken to get form fields by distance(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_9invoice.json :  0.0118560791015625
In Linear Form Fields
Time taken to get form fields by linear(path.exists): /home/<USER>/code/aavenir.invoiceflow.ai/Invoice/pdf/c90aff3f-3200-4f0b-970e-c0afa6faca22_9invoice.json :  0.6716408729553223
Time taken to get query results:  1.1341311931610107
c90aff3f-3200-4f0b-970e-c0afa6faca22 - all files cleaned ......
/home/<USER>/anaconda3/envs/apa/lib/python3.8/site-packages/django/db/models/fields/__init__.py:1420: RuntimeWarning: DateTimeField MetricCounter.completed_on received a naive datetime (2025-06-14 11:29:35.708571) while time zone support is active.
  warnings.warn("DateTimeField %s received a naive datetime (%s)"
Time taken to update status:  0.4880249500274658
Time taken to complete api:  704.0135266780853
[14/Jun/2025 11:29:36] "POST /Invoice/ HTTP/1.1" 200 287787